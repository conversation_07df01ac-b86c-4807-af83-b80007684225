<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import * as Select from '$lib/components/ui/select';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { AlertTriangle, CheckCircle } from '@lucide/svelte';
	import type { ColumnMapping, ProcessedRow } from '$lib/budget_import_utils';
	import { matchColumns, validateColumnMapping } from '$lib/budget_import_utils';

	interface Props {
		headers: string[];
		sampleRows: ProcessedRow[];
		onMappingChange: (mapping: ColumnMapping) => void;
		onNext: () => void;
		onBack: () => void;
	}

	let { headers, sampleRows, onMappingChange, onNext, onBack }: Props = $props();

	// Auto-detect column mapping
	let columnMapping = $state<ColumnMapping>(matchColumns(headers));

	// Available column types
	const columnTypes = [
		{ value: 'code', label: 'Code' },
		{ value: 'description', label: 'Description' },
		{ value: 'quantity', label: 'Quantity' },
		{ value: 'uom', label: 'Unit of Measure' },
		{ value: 'rate', label: 'Rate' },
		{ value: 'subtotal', label: 'SubTotal' },
		{ value: 'factor', label: 'Factor' },
		{ value: 'ignore', label: 'Ignore' },
	];

	// Validation
	let validation = $derived(validateColumnMapping(columnMapping));

	// Update parent when mapping changes
	$effect(() => {
		onMappingChange(columnMapping);
	});

	function updateMapping(headerIndex: number, columnType: string | null) {
		// Clear any existing mapping for this column type
		if (columnType && columnType !== 'ignore') {
			for (const [key, value] of Object.entries(columnMapping)) {
				if (value === headerIndex) {
					columnMapping[key as keyof ColumnMapping] = undefined;
				}
			}
			columnMapping[columnType as keyof ColumnMapping] = headerIndex;
		} else {
			// Clear mapping for this header index
			for (const [key, value] of Object.entries(columnMapping)) {
				if (value === headerIndex) {
					columnMapping[key as keyof ColumnMapping] = undefined;
				}
			}
		}

		// Trigger reactivity
		columnMapping = { ...columnMapping };
	}

	function getMappedColumnType(headerIndex: number): string | null {
		for (const [key, value] of Object.entries(columnMapping)) {
			if (value === headerIndex) {
				return key;
			}
		}
		return null;
	}

	function getSampleValues(headerIndex: number): string[] {
		// FIXME YOU ARE HERE
		const key = getMappedColumnType(headerIndex);
		if (!key) return [];

		return sampleRows
			.map((row) => row[key as keyof ProcessedRow])
			.filter((v) => v !== null && v !== undefined)
			.map(String);
	}
</script>

<p class="my-6">{JSON.stringify(sampleRows)}</p>

<div class="space-y-6">
	<div>
		<h2 class="mb-2 text-xl font-semibold">Step 3: Map Columns</h2>
		<p class="text-muted-foreground">
			Review the automatic column detection and adjust if needed. Use the drop down to select how
			the app will store the data from that column in your upload. Required columns are marked with
			a red asterisk.
		</p>
	</div>

	{#if !validation.isValid}
		<Alert variant="destructive">
			<AlertTriangle class="h-4 w-4" />
			<AlertDescription>
				<ul class="list-inside list-disc">
					{#each validation.errors as error (error)}
						<li>{error}</li>
					{/each}
				</ul>
			</AlertDescription>
		</Alert>
	{:else}
		<Alert>
			<CheckCircle class="h-4 w-4" />
			<AlertDescription>
				All required columns are mapped correctly. You can proceed to the next step.
			</AlertDescription>
		</Alert>
	{/if}

	<div class="grid gap-4">
		{#each headers as header, index (index)}
			<div class="rounded-lg border p-4">
				<div class="mb-2 flex items-center justify-between">
					<div class="flex items-center gap-2">
						<h3 class="font-medium">{header || `Column ${index + 1}`}</h3>
						{#if ['code', 'description', 'quantity', 'rate'].includes(getMappedColumnType(index) || '')}
							<Badge variant="outline" class="border-red-500 text-xs">Required</Badge>
						{/if}
					</div>
					<Select.Root
						type="single"
						value={getMappedColumnType(index) || 'ignore'}
						onValueChange={(v) => updateMapping(index, v === 'ignore' ? null : v)}
					>
						<Select.Trigger class="h-9 w-48">
							{#if getMappedColumnType(index)}
								{columnTypes.find((type) => type.value === getMappedColumnType(index))?.label}
							{:else}
								Ignore
							{/if}
						</Select.Trigger>
						<Select.Content>
							<Select.Group>
								<Select.Item value="ignore">Ignore</Select.Item>
								{#each columnTypes as type (type)}
									<Select.Item value={type.value}>{type.label}</Select.Item>
								{/each}
							</Select.Group>
						</Select.Content>
					</Select.Root>
				</div>

				{#if getSampleValues(index).length > 0}
					<div>
						<p class="text-muted-foreground mb-1 text-sm">Sample values:</p>
						<div class="flex flex-wrap gap-1">
							{#each getSampleValues(index).slice(0, 3) as value, i (i)}
								<Badge variant="outline" class="text-xs">{value}</Badge>
							{/each}
							{#if getSampleValues(index).length > 3}
								<Badge variant="outline" class="text-xs"
									>+{getSampleValues(index).length - 3} more</Badge
								>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		{/each}
	</div>

	<div class="flex justify-between">
		<Button variant="outline" onclick={onBack}>Back</Button>
		<Button onclick={onNext} disabled={!validation.isValid}>Next: Classify Rows</Button>
	</div>
</div>
