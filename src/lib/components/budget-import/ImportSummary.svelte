<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { <PERSON><PERSON>Triangle, CheckCircle, DollarSign, Hash, Layers } from '@lucide/svelte';
	import type { ClassifiedRow } from '$lib/budget_import_utils';
	import { transformToImportData, parseWbsCode } from '$lib/budget_import_utils';
	import { formatCurrency } from '$lib/utils';

	interface Props {
		classifiedRows: ClassifiedRow[];
		projectId: string;
		onImport: (importData: ReturnType<typeof transformToImportData>) => void;
		onBack: () => void;
		isImporting?: boolean;
	}

	let { classifiedRows, projectId, onImport, onBack, isImporting = false }: Props = $props();

	// Filter detail rows for import
	let detailRows = $derived(classifiedRows.filter((row) => row.classification === 'detail'));

	// Calculate statistics
	let totalItems = $derived(detailRows.length);
	let totalBudget = $derived(
		detailRows.reduce((sum, row) => {
			const quantity = row.quantity || 0;
			const rate = row.rate || 0;
			const factor = row.appliedFactor || row.factor || 1;
			return sum + quantity * rate * factor;
		}, 0),
	);

	// Get unique WBS levels that will be created
	let wbsLevels = $derived(() => {
		const levels = new Set<string>();
		detailRows.forEach((row) => {
			if (row.code) {
				try {
					const parsed = parseWbsCode(row.code);
					levels.add(`Level ${parsed.level}`);
				} catch (_e) {
					// Skip invalid codes
				}
			}
		});
		return Array.from(levels).sort();
	});

	// Validation warnings
	let warnings = $derived(() => {
		const warnings: string[] = [];

		detailRows.forEach((row, index) => {
			// Check for negative values
			if ((row.quantity || 0) < 0) {
				warnings.push(`Row ${index + 1}: Negative quantity (${row.quantity})`);
			}
			if ((row.rate || 0) < 0) {
				warnings.push(`Row ${index + 1}: Negative rate (${row.rate})`);
			}

			// Check for zero values
			if ((row.quantity || 0) === 0) {
				warnings.push(`Row ${index + 1}: Zero quantity`);
			}
			if ((row.rate || 0) === 0) {
				warnings.push(`Row ${index + 1}: Zero rate`);
			}

			// Check for missing required fields
			if (!row.code?.trim()) {
				warnings.push(`Row ${index + 1}: Missing WBS code`);
			}
			if (!row.finalDescription?.trim() && !row.description?.trim()) {
				warnings.push(`Row ${index + 1}: Missing description`);
			}
		});

		return warnings;
	});

	// Check if import should be blocked
	let hasErrors = $derived(warnings().some((w) => w.includes('Missing')));

	function handleImport() {
		const importData = transformToImportData(classifiedRows, projectId);
		onImport(importData);
	}
</script>

<div class="space-y-6">
	<div>
		<h2 class="mb-2 text-xl font-semibold">Step 5: Review Import</h2>
		<p class="text-muted-foreground">
			Review the import summary and resolve any warnings before proceeding.
		</p>
	</div>

	<!-- Statistics Cards -->
	<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">Items to Import</CardTitle>
				<Hash class="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{totalItems}</div>
				<p class="text-muted-foreground text-xs">Budget line items</p>
			</CardContent>
		</Card>

		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">Total Budget</CardTitle>
				<DollarSign class="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{formatCurrency(totalBudget)}</div>
				<p class="text-muted-foreground text-xs">Sum of quantity × rate × factor</p>
			</CardContent>
		</Card>

		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">WBS Levels</CardTitle>
				<Layers class="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{wbsLevels.length}</div>
				<p class="text-muted-foreground text-xs">
					{wbsLevels().join(', ')}
				</p>
			</CardContent>
		</Card>
	</div>

	<!-- Warnings -->
	{#if warnings.length > 0}
		<Alert variant={hasErrors ? 'destructive' : 'default'}>
			<AlertTriangle class="h-4 w-4" />
			<AlertDescription>
				<div class="mb-2 font-medium">
					{hasErrors ? 'Errors found - import blocked:' : 'Warnings found:'}
				</div>
				<ul class="max-h-32 list-inside list-disc space-y-1 overflow-y-auto">
					{#each warnings().slice(0, 10) as warning (warning)}
						<li class="text-sm">{warning}</li>
					{/each}
					{#if warnings().length > 10}
						<li class="text-sm font-medium">...and {warnings().length - 10} more</li>
					{/if}
				</ul>
			</AlertDescription>
		</Alert>
	{:else}
		<Alert>
			<CheckCircle class="h-4 w-4" />
			<AlertDescription>
				No issues found. Ready to import {totalItems} budget items.
			</AlertDescription>
		</Alert>
	{/if}

	<!-- Sample of items to be imported -->
	<Card>
		<CardHeader>
			<CardTitle class="text-lg">Items Preview</CardTitle>
		</CardHeader>
		<CardContent>
			<div class="max-h-180 space-y-2 overflow-y-auto">
				{#each detailRows as row, index (index)}
					<div class="flex items-center justify-between rounded border p-2 text-sm">
						<div class="flex-1">
							<div class="text-muted-foreground font-mono text-xs">{row.code}</div>
							<div class="font-medium">{row.finalDescription || row.description}</div>
						</div>
						<div class="text-right">
							<div>{row.quantity} {row.uom || ''}</div>
							<div class="text-muted-foreground">
								@ {formatCurrency(row.rate || 0)}
								{#if row.factor && row.factor !== 1}
									× {row.factor}
								{/if}
							</div>
						</div>
					</div>
				{/each}
			</div>
		</CardContent>
	</Card>

	<div class="flex justify-between">
		<Button variant="outline" onclick={onBack} disabled={isImporting}>Back</Button>
		<Button
			onclick={handleImport}
			disabled={hasErrors || isImporting || totalItems === 0}
			class="min-w-32"
		>
			{#if isImporting}
				Importing...
			{:else}
				Import {totalItems} Items
			{/if}
		</Button>
	</div>
</div>
