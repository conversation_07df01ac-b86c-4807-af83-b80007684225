<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Badge } from '$lib/components/ui/badge';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { Info, Pencil } from '@lucide/svelte';
	import { Select, SelectContent, SelectItem, SelectTrigger } from '$lib/components/ui/select';
	import type { ProcessedRow, ClassifiedRow, ColumnMapping } from '$lib/budget_import_utils';
	import { applyCategoriesToRows, collectIsolatedCategories } from '$lib/budget_import_utils';

	interface Props {
		rows: ProcessedRow[];
		columnMapping: ColumnMapping;
		onClassifiedRowsChange: (classifiedRows: ClassifiedRow[]) => void;
		onNext: () => void;
		onBack: () => void;
	}

	let { rows, columnMapping, onClassifiedRowsChange, onNext, onBack }: Props = $props();

	// Category edits (row index -> edited category name)
	let categoryEdits = $state<Record<number, string>>({});

	// Manual category selections (row index -> selected category names)
	let manualCategorySelections = $state<Record<number, string[]>>({});

	// Editing state
	let editingRow = $state<number | null>(null);
	let editingValue = $state<string>('');

	// Collect isolated categories for manual selection
	let isolatedCategories = $derived(collectIsolatedCategories(rows));

	// Apply categories and get classified rows
	let classifiedRows = $derived(
		applyCategoriesToRows(rows, categoryEdits, manualCategorySelections),
	);

	// Count detail rows for summary
	let detailRowCount = $derived(
		classifiedRows.filter((row) => row.classification === 'detail').length,
	);

	// Update parent when classified rows change
	$effect(() => {
		onClassifiedRowsChange(classifiedRows);
	});

	function startEditing(rowIndex: number, currentValue: string) {
		editingRow = rowIndex;
		editingValue = currentValue;
	}

	function saveEdit() {
		if (editingRow !== null) {
			categoryEdits[editingRow] = editingValue;
			categoryEdits = { ...categoryEdits }; // Trigger reactivity
			editingRow = null;
			editingValue = '';
		}
	}

	function cancelEdit() {
		editingRow = null;
		editingValue = '';
	}

	function handleManualCategoryChange(rowIndex: number, selectedCategories: string[]) {
		manualCategorySelections[rowIndex] = selectedCategories;
		manualCategorySelections = { ...manualCategorySelections }; // Trigger reactivity
	}

	function getRowValue(row: ProcessedRow, columnType: keyof ColumnMapping): string {
		const columnIndex = columnMapping[columnType];
		if (columnIndex === undefined) return '';

		switch (columnType) {
			case 'code':
				return row.code || '';
			case 'description':
				return row.description || '';
			case 'quantity':
				return row.quantity?.toString() || '';
			case 'uom':
				return row.uom || '';
			case 'rate':
				return row.rate?.toString() || '';
			case 'subtotal':
				return row.subtotal?.toString() || '';
			case 'factor':
				return row.factor?.toString() || '';
			default:
				return '';
		}
	}

	function getClassificationColor(classification: string): string {
		switch (classification) {
			case 'detail':
				return 'bg-green-100 text-green-800';
			case 'category':
				return 'bg-blue-100 text-blue-800';
			case 'summary':
				return 'bg-yellow-100 text-yellow-800';
			case 'ignore':
				return 'bg-gray-100 text-gray-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	}
</script>

<div class="space-y-6">
	<div>
		<h2 class="mb-2 text-xl font-semibold">Step 4: Classify Rows</h2>
		<p class="text-muted-foreground">
			Review how rows have been classified. Edit category names to customize the prefixes that will
			be added to detail rows.
		</p>
	</div>

	<Alert>
		<Info class="h-4 w-4" />
		<AlertDescription>
			Found {detailRowCount} detail rows that will be imported. Category rows will be used to prefix
			detail row descriptions. Use the dropdown in the Code column to manually prepend additional categories.
		</AlertDescription>
	</Alert>

	<div class="overflow-hidden rounded-lg border">
		<div class="bg-muted grid grid-cols-12 gap-2 p-2 text-sm font-medium">
			<div class="col-span-1">Type</div>
			<div class="col-span-2">Code</div>
			<div class="col-span-4">Description</div>
			<div class="col-span-2">Final Description</div>
			<div class="col-span-1">Quantity</div>
			<div class="col-span-1">Rate</div>
			<div class="col-span-1">Actions</div>
		</div>

		<div class="max-h-96 overflow-y-auto">
			{#each classifiedRows.slice(0, 50) as row, index (index)}
				<div
					class="grid grid-cols-12 items-center gap-2 border-b p-2 text-sm"
					class:bg-gray-50={row.classification === 'ignore'}
					class:pl-6={row.classification === 'detail' && row.categoryPrefix}
				>
					<!-- Classification -->
					<div class="col-span-1">
						<Badge class={getClassificationColor(row.classification)}>
							{row.classification}
						</Badge>
					</div>

					<!-- Code -->
					<div class="col-span-2 font-mono text-xs">
						{#if row.classification === 'category' && isolatedCategories.length > 0}
							<Select
								type="multiple"
								value={manualCategorySelections[index] || []}
								onValueChange={(selectedCategories) =>
									handleManualCategoryChange(index, selectedCategories || [])}
							>
								<SelectTrigger class="h-6 text-xs">
									{#if manualCategorySelections[index]?.length}
										{manualCategorySelections[index].length} selected
									{:else}
										Select categories
									{/if}
								</SelectTrigger>
								<SelectContent>
									{#each isolatedCategories as category (category)}
										<SelectItem value={category} label={category}>
											{category}
										</SelectItem>
									{/each}
								</SelectContent>
							</Select>
						{:else}
							{getRowValue(row, 'code')}
						{/if}
					</div>

					<!-- Original Description -->
					<div class="col-span-4">
						{#if row.classification === 'category' && editingRow === index}
							<div class="flex gap-1">
								<Input
									bind:value={editingValue}
									class="h-6 text-xs"
									onkeydown={(e) => {
										if (e.key === 'Enter') saveEdit();
										if (e.key === 'Escape') cancelEdit();
									}}
								/>
								<Button size="sm" variant="ghost" onclick={saveEdit} class="h-6 px-1">✓</Button>
								<Button size="sm" variant="ghost" onclick={cancelEdit} class="h-6 px-1">✕</Button>
							</div>
						{:else}
							<span class:line-through={row.classification === 'ignore'}>
								{getRowValue(row, 'description')}
							</span>
						{/if}
					</div>

					<!-- Final Description (with prefixes) -->
					<div class="col-span-2">
						{#if row.finalDescription && row.finalDescription !== getRowValue(row, 'description')}
							<div class="text-xs">
								{#if row.classification === 'category' && row.manualCategories?.length}
									<span class="font-medium text-purple-600">
										{row.manualCategories.map((cat) => `[${cat}]`).join('')}
									</span>
								{/if}
								{#if row.categoryPrefix}
									<span class="font-medium text-blue-600">{row.categoryPrefix}</span>
								{/if}
								<span>{getRowValue(row, 'description')}</span>
							</div>
						{:else if row.classification === 'category' && row.manualCategories?.length}
							<div class="text-xs">
								<span class="font-medium text-purple-600">
									{row.manualCategories.map((cat) => `[${cat}]`).join('')}
								</span>
								<span>{getRowValue(row, 'description')}</span>
							</div>
						{:else if row.classification === 'detail'}
							<span class="text-muted-foreground text-xs">No prefix</span>
						{/if}
					</div>

					<!-- Quantity -->
					<div class="col-span-1 text-right">
						{getRowValue(row, 'quantity')}
					</div>

					<!-- Rate -->
					<div class="col-span-1 text-right">
						{getRowValue(row, 'rate')}
					</div>

					<!-- Actions -->
					<div class="col-span-1">
						{#if row.classification === 'category' && editingRow !== index}
							<Button
								size="sm"
								variant="ghost"
								onclick={() => startEditing(index, getRowValue(row, 'description'))}
								class="h-6 w-6 p-0"
							>
								<Pencil class="h-3 w-3" />
							</Button>
						{/if}
					</div>
				</div>
			{/each}

			{#if classifiedRows.length > 50}
				<div class="text-muted-foreground p-4 text-center">
					Showing first 50 rows of {classifiedRows.length}. All rows will be processed during
					import.
				</div>
			{/if}
		</div>
	</div>

	<div class="flex justify-between">
		<Button variant="outline" onclick={onBack}>Back</Button>
		<Button onclick={onNext} disabled={detailRowCount === 0}>Next: Review Import</Button>
	</div>
</div>
